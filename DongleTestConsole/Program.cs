using System.Runtime.InteropServices;
using DongleTestConsole;
using System.Text;

Console.OutputEncoding = Encoding.UTF8;
Console.WriteLine("=== 加密锁测试控制台程序 ===");
Console.WriteLine($"程序启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
Console.WriteLine();

   //RSA公钥格式(兼容1024,2048)
    [StructLayout(LayoutKind.Sequential)]
    public struct RSA_PUBLIC_KEY
    {
        public uint bits;                   // length in bits of modulus        	
        public uint modulus;				  // modulus
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
        public byte[] exponent;       // public exponent
    }

    //RSA私钥格式(兼容1024,2048)
    [StructLayout(LayoutKind.Sequential)]
    public struct RSA_PRIVATE_KEY
    {
        public uint bits;                   // length in bits of modulus        	
        public uint modulus;				  // modulus  
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
        public byte[] publicExponent;       // public exponent
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
        public byte[] exponent;       // public exponent
    }

    //外部ECCSM2公钥格式 ECC(支持bits为192或256)和SM2的(bits为固定值0x8100)公钥格式
    [StructLayout(LayoutKind.Sequential)]
    public struct ECCSM2_PUBLIC_KEY
    {
        public uint bits;                   // length in bits of modulus        	
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public uint[] XCoordinate;       // 曲线上点的X坐标
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public uint[] YCoordinate;       // 曲线上点的Y坐标
    }

    //外部ECCSM2私钥格式 ECC(支持bits为192或256)和SM2的(bits为固定值0x8100)私钥格式  
    [StructLayout(LayoutKind.Sequential)]
    public struct ECCSM2_PRIVATE_KEY
    {
        public uint bits;                   // length in bits of modulus        	
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public uint[] PrivateKey;           // 私钥
    }

    //加密锁信息
    [StructLayout(LayoutKind.Sequential)]
    public struct DONGLE_INFO
    {
        public ushort m_Ver;               //COS版本,比如:0x0201,表示2.01版             	
        public ushort m_Type;              //产品类型: 0xFF表示标准版, 0x00为时钟锁,0x01为带时钟的U盘锁,0x02为标准U盘锁  
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public byte[] m_BirthDay;       //出厂日期 
        public uint m_Agent;             //代理商编号,比如:默认的0xFFFFFFFF
        public uint m_PID;               //产品ID
        public uint m_UserID;            //用户ID
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public byte[] m_HID;            //8字节的硬件ID
        public uint m_IsMother;          //母锁标志: 0x01表示是母锁, 0x00表示不是母锁
        public uint m_DevType;           //设备类型(PROTOCOL_HID或者PROTOCOL_CCID)
    }

    //数据文件授权结构
    [StructLayout(LayoutKind.Sequential)]
    public struct DATA_LIC
    {
        public ushort m_Read_Priv;     //读权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限            	
        public ushort m_Write_Priv;    //写权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    }

    //私钥文件授权结构
    [StructLayout(LayoutKind.Sequential)]
    public struct PRIKEY_LIC
    {
        public uint m_Count;        //可调次数: 0xFFFFFFFF表示不限制, 递减到0表示已不可调用
        public byte m_Priv;         //调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
        public byte m_IsDecOnRAM;   //是否是在内存中递减: 1为在内存中递减，0为在FLASH中递减
        public byte m_IsReset;      //用户态调用后是否自动回到匿名态: TRUE为调后回到匿名态 (开发商态不受此限制)
        public byte m_Reserve;      //保留,用于4字节对齐
    }

    //对称加密算法(SM4/TDES)密钥文件授权结构
    [StructLayout(LayoutKind.Sequential)]
    public struct KEY_LIC
    {
        public uint m_Priv_Enc;   //加密时的调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    }

    //可执行文件授权结构
    [StructLayout(LayoutKind.Sequential)]
    public struct EXE_LIC
    {
        public ushort m_Priv_Exe;   //运行的权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    }

    //数据文件属性数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct DATA_FILE_ATTR
    {
        public uint m_Size;      //数据文件长度，该值最大为4096
        public DATA_LIC m_Lic;       //授权
    }

    //ECCSM2/RSA私钥文件属性数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct PRIKEY_FILE_ATTR
    {
        public ushort m_Type;       //数据类型:ECCSM2私钥 或 RSA私钥
        public ushort m_Size;       //数据长度:RSA该值为1024或2048, ECC该值为192或256, SM2该值为0x8100
        public PRIKEY_LIC m_Lic;        //授权
    }

    //对称加密算法(SM4/TDES)密钥文件属性数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct KEY_FILE_ATTR
    {
        public uint m_Size;       //密钥数据长度=16
        public KEY_LIC m_Lic;        //授权
    }

    //可执行文件属性数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct EXE_FILE_ATTR
    {
        public EXE_LIC m_Lic;        //授权	
        public ushort m_Len;        //文件长度
    }

    //获取私钥文件列表时返回的数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct PRIKEY_FILE_LIST
    {
        public ushort m_FILEID;  //文件ID
        public ushort m_Reserve; //保留,用于4字节对齐
        public PRIKEY_FILE_ATTR m_attr;    //文件属性
    }

    //获取SM4及TDES密钥文件列表时返回的数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct KEY_FILE_LIST
    {
        public ushort m_FILEID;  //文件ID
        public ushort m_Reserve; //保留,用于4字节对齐
        public KEY_FILE_ATTR m_attr;    //文件属性
    }

    //获取数据文件列表时返回的数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct DATA_FILE_LIST
    {
        public ushort m_FILEID;  //文件ID
        public ushort m_Reserve; //保留,用于4字节对齐
        public DATA_FILE_ATTR m_attr;    //文件属性
    }

    //获取可执行文件列表时返回的数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct EXE_FILE_LIST
    {
        public ushort m_FILEID;    //文件ID
        public EXE_FILE_ATTR m_attr;
        public ushort m_Reserve;  //保留,用于4字节对齐
    }

    //下载和列可执行文件时填充的数据结构
    [StructLayout(LayoutKind.Sequential)]
    public struct EXE_FILE_INFO
    {
        public ushort m_dwSize;           //可执行文件大小
        public ushort m_wFileID;          //可执行文件ID
        public byte m_Priv;             //调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
        public byte[] m_pData;            //可执行文件数据
    }

    //需要发给空锁的初始化数据
    [StructLayout(LayoutKind.Sequential)]
    public struct SON_DATA
    {
        public int m_SeedLen;                 //种子码长度
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string m_SeedForPID;	       //产生产品ID和开发商密码的种子码 (最长250个字节)
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 18)]
        public string m_UserPIN;         //用户密码(16个字符的0终止字符串)
        public sbyte m_UserTryCount;            //用户密码允许的最大错误重试次数
        public sbyte m_AdminTryCount;           //开发商密码允许的最大错误重试次数
        public int m_UserID_Start;            //起始用户ID
    }

    //母锁数据
    [StructLayout(LayoutKind.Sequential)]
    public struct MOTHER_DATA
    {
        public SON_DATA m_Son;                  //子锁初始化数据
        public int m_Count;                //可产生子锁初始化数据的次数 (-1表示不限制次数, 递减到0时会受限)
    }

// 常量定义
    public const string DLL_NAME = "Dongle_d.dll";

    #region DLL导入声明

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_Enum(ref DONGLE_INFO pDongleInfo, out ushort pCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_Open(ref uint phDongle, int nIndex);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_Close(uint hDongle);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_VerifyPIN(uint hDongle, uint nFlags, byte[] pPIN, out int pRemainCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_CreateFile(uint hDongle, uint nFileType, ushort wFileID, uint pFileAttr);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_WriteFile(uint hDongle, uint nFileType, ushort wFileID, short wOffset, byte[] buffer, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ReadFile(uint hDongle, short wFileID, short wOffset, byte[] buffer, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ListFile(uint hDongle, uint nFileType, DATA_FILE_LIST[] pFileList, ref int pDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_DeleteFile(uint hDongle, uint nFileType, short wFileID);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_DownloadExeFile(uint hDongle, EXE_FILE_INFO[] pExeFileInfo, int nCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RunExeFile(uint hDongle, short wFileID, byte[] pInOutData, short wInOutDataLen, ref int nMainRet);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_WriteShareMemory(uint hDongle, byte[] pData, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ReadShareMemory(uint hDongle, byte[] pData);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_WriteData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ReadData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_LEDControl(uint hDongle, uint nFlag);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SwitchProtocol(uint hDongle, uint nFlag);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_GetUTCTime(uint hDongle, ref uint pdwUTCTime);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SetDeadline(uint hDongle, uint dwTime);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_GenUniqueKey(uint hDongle, int nSeedLen, byte[] pSeed, byte[] pPIDstr, byte[] pAdminPINstr);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ResetState(uint hDongle);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ChangePIN(uint hDongle, uint nFlags, byte[] pOldPIN, byte[] pNewPIN, int nTryCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RFS(uint hDongle);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SetUserID(uint hDongle, uint dwUserID);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ResetUserPIN(uint hDongle, byte[] pAdminPIN);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RsaGenPubPriKey(uint hDongle, ushort wPriFileID, ref RSA_PUBLIC_KEY pPubBakup, ref RSA_PRIVATE_KEY pPriBakup);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RsaPri(uint hDongle, ushort wPriFileID, uint nFlag, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RsaPub(uint hDongle, uint nFlag, ref RSA_PUBLIC_KEY pPubKey, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_TDES(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SM4(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_HASH(uint hDongle, uint nFlag, byte[] pInData, uint nDataLen, byte[] pHash);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_LimitSeedCount(uint hDongle, int nCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_Seed(uint hDongle, byte[] pSeed, uint nSeedLen, byte[] pOutData);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_EccGenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY vPubBakup, ref ECCSM2_PRIVATE_KEY vPriBakup);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_EccSign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_EccVerify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SM2GenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY pPubBakup, ref ECCSM2_PRIVATE_KEY pPriBakup);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SM2Sign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SM2Verify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

    #endregion

    #region 错误代码定义

    /// <summary>
    /// 获取错误代码的描述
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <returns>错误描述</returns>
    public static string GetErrorDescription(uint errorCode)
    {
        return errorCode switch
        {
            0x00000000 => "成功",
            0x00000001 => "无效的参数",
            0x00000002 => "内存不足",
            0x00000003 => "设备未找到",
            0x00000004 => "设备已打开",
            0x00000005 => "设备未打开",
            0x00000006 => "通信错误",
            0x00000007 => "操作超时",
            0x00000008 => "权限不足",
            0x00000009 => "文件不存在",
            0x0000000A => "文件已存在",
            0x0000000B => "空间不足",
            0x0000000C => "PIN码错误",
            0x0000000D => "PIN码被锁定",
            0x0000000E => "操作被拒绝",
            0x0000000F => "数据校验错误",

            // 厂商特定错误代码
            0xF0000001 => "设备通信失败",
            0xF0000002 => "设备句柄无效或设备已断开连接",
            0xF0000003 => "设备忙碌或被其他程序占用",
            0xF0000004 => "设备驱动程序错误",
            0xF0000005 => "设备固件错误",

            _ => $"未知错误代码: 0x{errorCode:X8}"
        };
    }

    #endregion


    Console.WriteLine();
    Console.WriteLine("开始执行基本功能测试...");
    Console.WriteLine();
                
    var basicResult = await tester.RunBasicTestAsync();
    Console.WriteLine(basicResult);

    Console.WriteLine();
    Console.WriteLine("按任意键继续...");
    Console.ReadKey();
    Console.Clear();
    
    Console.WriteLine("=== 加密锁测试控制台程序 ===");
    Console.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
    Console.WriteLine();

