using System.Runtime.InteropServices;
using System.Text;
using System.Diagnostics;

// ===== 数据结构定义 =====

//RSA公钥格式(兼容1024,2048)
[StructLayout(LayoutKind.Sequential)]
public struct RSA_PUBLIC_KEY
{
    public uint bits;                   // length in bits of modulus
    public uint modulus;				  // modulus
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
    public byte[] exponent;       // public exponent
}

//RSA私钥格式(兼容1024,2048)
[StructLayout(LayoutKind.Sequential)]
public struct RSA_PRIVATE_KEY
{
    public uint bits;                   // length in bits of modulus
    public uint modulus;				  // modulus
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
    public byte[] publicExponent;       // public exponent
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
    public byte[] exponent;       // public exponent
}

//外部ECCSM2公钥格式 ECC(支持bits为192或256)和SM2的(bits为固定值0x8100)公钥格式
[StructLayout(LayoutKind.Sequential)]
public struct ECCSM2_PUBLIC_KEY
{
    public uint bits;                   // length in bits of modulus
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public uint[] XCoordinate;       // 曲线上点的X坐标
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public uint[] YCoordinate;       // 曲线上点的Y坐标
}

//外部ECCSM2私钥格式 ECC(支持bits为192或256)和SM2的(bits为固定值0x8100)私钥格式
[StructLayout(LayoutKind.Sequential)]
public struct ECCSM2_PRIVATE_KEY
{
    public uint bits;                   // length in bits of modulus
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public uint[] PrivateKey;           // 私钥
}

//加密锁信息
[StructLayout(LayoutKind.Sequential)]
public struct DONGLE_INFO
{
    public ushort m_Ver;               //COS版本,比如:0x0201,表示2.01版
    public ushort m_Type;              //产品类型: 0xFF表示标准版, 0x00为时钟锁,0x01为带时钟的U盘锁,0x02为标准U盘锁
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public byte[] m_BirthDay;       //出厂日期
    public uint m_Agent;             //代理商编号,比如:默认的0xFFFFFFFF
    public uint m_PID;               //产品ID
    public uint m_UserID;            //用户ID
    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
    public byte[] m_HID;            //8字节的硬件ID
    public uint m_IsMother;          //母锁标志: 0x01表示是母锁, 0x00表示不是母锁
    public uint m_DevType;           //设备类型(PROTOCOL_HID或者PROTOCOL_CCID)
}

//数据文件授权结构
[StructLayout(LayoutKind.Sequential)]
public struct DATA_LIC
{
    public ushort m_Read_Priv;     //读权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    public ushort m_Write_Priv;    //写权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
}

//私钥文件授权结构
[StructLayout(LayoutKind.Sequential)]
public struct PRIKEY_LIC
{
    public uint m_Count;        //可调次数: 0xFFFFFFFF表示不限制, 递减到0表示已不可调用
    public byte m_Priv;         //调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    public byte m_IsDecOnRAM;   //是否是在内存中递减: 1为在内存中递减，0为在FLASH中递减
    public byte m_IsReset;      //用户态调用后是否自动回到匿名态: TRUE为调后回到匿名态 (开发商态不受此限制)
    public byte m_Reserve;      //保留,用于4字节对齐
}

//对称加密算法(SM4/TDES)密钥文件授权结构
[StructLayout(LayoutKind.Sequential)]
public struct KEY_LIC
{
    public uint m_Priv_Enc;   //加密时的调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
}

//可执行文件授权结构
[StructLayout(LayoutKind.Sequential)]
public struct EXE_LIC
{
    public ushort m_Priv_Exe;   //运行的权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
}

//数据文件属性数据结构
[StructLayout(LayoutKind.Sequential)]
public struct DATA_FILE_ATTR
{
    public uint m_Size;      //数据文件长度，该值最大为4096
    public DATA_LIC m_Lic;       //授权
}

//ECCSM2/RSA私钥文件属性数据结构
[StructLayout(LayoutKind.Sequential)]
public struct PRIKEY_FILE_ATTR
{
    public ushort m_Type;       //数据类型:ECCSM2私钥 或 RSA私钥
    public ushort m_Size;       //数据长度:RSA该值为1024或2048, ECC该值为192或256, SM2该值为0x8100
    public PRIKEY_LIC m_Lic;        //授权
}

//对称加密算法(SM4/TDES)密钥文件属性数据结构
[StructLayout(LayoutKind.Sequential)]
public struct KEY_FILE_ATTR
{
    public uint m_Size;       //密钥数据长度=16
    public KEY_LIC m_Lic;        //授权
}

//可执行文件属性数据结构
[StructLayout(LayoutKind.Sequential)]
public struct EXE_FILE_ATTR
{
    public EXE_LIC m_Lic;        //授权
    public ushort m_Len;        //文件长度
}

//获取私钥文件列表时返回的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct PRIKEY_FILE_LIST
{
    public ushort m_FILEID;  //文件ID
    public ushort m_Reserve; //保留,用于4字节对齐
    public PRIKEY_FILE_ATTR m_attr;    //文件属性
}

//获取SM4及TDES密钥文件列表时返回的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct KEY_FILE_LIST
{
    public ushort m_FILEID;  //文件ID
    public ushort m_Reserve; //保留,用于4字节对齐
    public KEY_FILE_ATTR m_attr;    //文件属性
}

//获取数据文件列表时返回的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct DATA_FILE_LIST
{
    public ushort m_FILEID;  //文件ID
    public ushort m_Reserve; //保留,用于4字节对齐
    public DATA_FILE_ATTR m_attr;    //文件属性
}

//获取可执行文件列表时返回的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct EXE_FILE_LIST
{
    public ushort m_FILEID;    //文件ID
    public EXE_FILE_ATTR m_attr;
    public ushort m_Reserve;  //保留,用于4字节对齐
}

//下载和列可执行文件时填充的数据结构
[StructLayout(LayoutKind.Sequential)]
public struct EXE_FILE_INFO
{
    public ushort m_dwSize;           //可执行文件大小
    public ushort m_wFileID;          //可执行文件ID
    public byte m_Priv;             //调用权限: 0为最小匿名权限，1为最小用户权限，2为最小开发商权限
    public byte[] m_pData;            //可执行文件数据
}

//需要发给空锁的初始化数据
[StructLayout(LayoutKind.Sequential)]
public struct SON_DATA
{
    public int m_SeedLen;                 //种子码长度
    [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
    public string m_SeedForPID;	       //产生产品ID和开发商密码的种子码 (最长250个字节)
    [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 18)]
    public string m_UserPIN;         //用户密码(16个字符的0终止字符串)
    public sbyte m_UserTryCount;            //用户密码允许的最大错误重试次数
    public sbyte m_AdminTryCount;           //开发商密码允许的最大错误重试次数
    public int m_UserID_Start;            //起始用户ID
}

//母锁数据
[StructLayout(LayoutKind.Sequential)]
public struct MOTHER_DATA
{
    public SON_DATA m_Son;                  //子锁初始化数据
    public int m_Count;                //可产生子锁初始化数据的次数 (-1表示不限制次数, 递减到0时会受限)
}

// ===== 主程序类 =====

public class Program
{
    // ===== API 函数声明 =====

    const string DLL_NAME = "Dongle_d.dll";

[DllImport(DLL_NAME)]
static extern uint Dongle_Enum(ref DONGLE_INFO pDongleInfo, out ushort pCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_Open(ref uint phDongle, int nIndex);

[DllImport(DLL_NAME)]
static extern uint Dongle_Close(uint hDongle);

[DllImport(DLL_NAME)]
static extern uint Dongle_VerifyPIN(uint hDongle, uint nFlags, byte[] pPIN, out int pRemainCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_CreateFile(uint hDongle, uint nFileType, ushort wFileID, uint pFileAttr);

[DllImport(DLL_NAME)]
static extern uint Dongle_WriteFile(uint hDongle, uint nFileType, ushort wFileID, short wOffset, byte[] buffer, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_ReadFile(uint hDongle, short wFileID, short wOffset, byte[] buffer, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_ListFile(uint hDongle, uint nFileType, DATA_FILE_LIST[] pFileList, ref int pDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_DeleteFile(uint hDongle, uint nFileType, short wFileID);

[DllImport(DLL_NAME)]
static extern uint Dongle_DownloadExeFile(uint hDongle, EXE_FILE_INFO[] pExeFileInfo, int nCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_RunExeFile(uint hDongle, short wFileID, byte[] pInOutData, short wInOutDataLen, ref int nMainRet);

[DllImport(DLL_NAME)]
static extern uint Dongle_WriteShareMemory(uint hDongle, byte[] pData, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_ReadShareMemory(uint hDongle, byte[] pData);

[DllImport(DLL_NAME)]
static extern uint Dongle_WriteData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_ReadData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_LEDControl(uint hDongle, uint nFlag);

[DllImport(DLL_NAME)]
static extern uint Dongle_SwitchProtocol(uint hDongle, uint nFlag);

[DllImport(DLL_NAME)]
static extern uint Dongle_GetUTCTime(uint hDongle, ref uint pdwUTCTime);

[DllImport(DLL_NAME)]
static extern uint Dongle_SetDeadline(uint hDongle, uint dwTime);

[DllImport(DLL_NAME)]
static extern uint Dongle_GenUniqueKey(uint hDongle, int nSeedLen, byte[] pSeed, byte[] pPIDstr, byte[] pAdminPINstr);

[DllImport(DLL_NAME)]
static extern uint Dongle_ResetState(uint hDongle);

[DllImport(DLL_NAME)]
static extern uint Dongle_ChangePIN(uint hDongle, uint nFlags, byte[] pOldPIN, byte[] pNewPIN, int nTryCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_RFS(uint hDongle);

[DllImport(DLL_NAME)]
static extern uint Dongle_SetUserID(uint hDongle, uint dwUserID);

[DllImport(DLL_NAME)]
static extern uint Dongle_ResetUserPIN(uint hDongle, byte[] pAdminPIN);

[DllImport(DLL_NAME)]
static extern uint Dongle_RsaGenPubPriKey(uint hDongle, ushort wPriFileID, ref RSA_PUBLIC_KEY pPubBakup, ref RSA_PRIVATE_KEY pPriBakup);

[DllImport(DLL_NAME)]
static extern uint Dongle_RsaPri(uint hDongle, ushort wPriFileID, uint nFlag, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_RsaPub(uint hDongle, uint nFlag, ref RSA_PUBLIC_KEY pPubKey, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_TDES(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_SM4(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

[DllImport(DLL_NAME)]
static extern uint Dongle_HASH(uint hDongle, uint nFlag, byte[] pInData, uint nDataLen, byte[] pHash);

[DllImport(DLL_NAME)]
static extern uint Dongle_LimitSeedCount(uint hDongle, int nCount);

[DllImport(DLL_NAME)]
static extern uint Dongle_Seed(uint hDongle, byte[] pSeed, uint nSeedLen, byte[] pOutData);

[DllImport(DLL_NAME)]
static extern uint Dongle_EccGenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY vPubBakup, ref ECCSM2_PRIVATE_KEY vPriBakup);

[DllImport(DLL_NAME)]
static extern uint Dongle_EccSign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

[DllImport(DLL_NAME)]
static extern uint Dongle_EccVerify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

[DllImport(DLL_NAME)]
static extern uint Dongle_SM2GenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY pPubBakup, ref ECCSM2_PRIVATE_KEY pPriBakup);

[DllImport(DLL_NAME)]
static extern uint Dongle_SM2Sign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

[DllImport(DLL_NAME)]
static extern uint Dongle_SM2Verify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

// ===== 错误处理函数 =====

static string GetErrorDescription(uint errorCode)
{
    return errorCode switch
    {
        0x00000000 => "成功",
        0x00000001 => "无效的参数",
        0x00000002 => "内存不足",
        0x00000003 => "设备未找到",
        0x00000004 => "设备已打开",
        0x00000005 => "设备未打开",
        0x00000006 => "通信错误",
        0x00000007 => "操作超时",
        0x00000008 => "权限不足",
        0x00000009 => "文件不存在",
        0x0000000A => "文件已存在",
        0x0000000B => "空间不足",
        0x0000000C => "PIN码错误",
        0x0000000D => "PIN码被锁定",
        0x0000000E => "操作被拒绝",
        0x0000000F => "数据校验错误",

        // 厂商特定错误代码
        0xF0000001 => "设备通信失败",
        0xF0000002 => "设备句柄无效或设备已断开连接",
        0xF0000003 => "设备忙碌或被其他程序占用",
        0xF0000004 => "设备驱动程序错误",
        0xF0000005 => "设备固件错误",

        _ => $"未知错误代码: 0x{errorCode:X8}"
    };
}

// ===== 全局变量 =====

static List<uint> openHandles = new();

// ===== 主要测试函数 =====

static async Task<string> RunBasicTestAsync()
{
    var result = new StringBuilder();
    var stopwatch = Stopwatch.StartNew();

    try
    {
        result.AppendLine("=== 加密锁基本功能测试 ===");
        result.AppendLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        result.AppendLine();

        // 1. 第一次枚举加密锁：获取设备数量
        result.AppendLine("步骤1: 枚举加密锁设备...");
        var pDongleInfo = new DONGLE_INFO();
        ushort pCount = 0;
        uint ret = Dongle_Enum(ref pDongleInfo, out pCount);

        if (ret != 0)
        {
            result.AppendLine($"❌ Enum Dongle Failed! Return value: 0x{ret:X8}");
            result.AppendLine($"   错误描述: {GetErrorDescription(ret)}");
            return result.ToString();
        }

        result.AppendLine($"✅ Enum Dongle Success! Count: {pCount}");

        if (pCount == 0)
        {
            result.AppendLine("⚠️  未检测到加密锁设备");
            return result.ToString();
        }

        // 2. 第二次枚举加密锁：获取设备详细信息
        result.AppendLine();
        result.AppendLine("步骤2: 获取设备详细信息...");
        ret = Dongle_Enum(ref pDongleInfo, out pCount);
        if (ret != 0)
        {
            result.AppendLine($"❌ GetInfo Dongle Failed! Return value: 0x{ret:X8}");
            result.AppendLine($"   错误描述: {GetErrorDescription(ret)}");
            return result.ToString();
        }

        result.AppendLine("✅ GetInfo Dongle Success!");

        // 3. 显示设备信息（按照WinForm的格式）
        result.AppendLine();
        for (int k = 0; k < pCount; k++)
        {
            result.AppendLine($"********** Dongle ARM INFO (Index: {k}) **********");
            result.AppendLine($"Agent ID: 0x{pDongleInfo.m_Agent:X}");
            result.AppendLine($"Dev Type: {pDongleInfo.m_DevType}");

            result.Append("HID: ");
            for (int i = 0; i < 8; i++)
            {
                result.Append($"{pDongleInfo.m_HID[i]:X2} ");
            }
            result.AppendLine();

            result.AppendLine($"Birth day: 20{pDongleInfo.m_BirthDay[0]:X2}-{pDongleInfo.m_BirthDay[1]:X2}-{pDongleInfo.m_BirthDay[2]:X2} " +
                            $"{pDongleInfo.m_BirthDay[3]:X2}:{pDongleInfo.m_BirthDay[4]:X2}:{pDongleInfo.m_BirthDay[5]:X2}");
            result.AppendLine($"Is Mother Dongle: {pDongleInfo.m_IsMother}");
            result.AppendLine($"PID: 0x{pDongleInfo.m_PID:X}");
            result.AppendLine($"Product Type: 0x{pDongleInfo.m_Type:X}");
            result.AppendLine($"UID: 0x{pDongleInfo.m_UserID:X}");
            result.AppendLine();
        }

        // 4. 测试打开和关闭操作
        result.AppendLine("步骤3: 测试打开和关闭操作...");
        await TestOpenCloseOperationsAsync(result, pCount);

    }
    catch (Exception ex)
    {
        result.AppendLine($"❌ 测试过程中发生异常: {ex.Message}");
        result.AppendLine($"   异常堆栈: {ex.StackTrace}");
    }
    finally
    {
        // 确保清理所有打开的句柄
        await CleanupHandlesAsync(result);

        stopwatch.Stop();
        result.AppendLine();
        result.AppendLine($"测试结束时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        result.AppendLine($"总耗时: {stopwatch.ElapsedMilliseconds} ms");
    }

    return result.ToString();
}

static async Task TestOpenCloseOperationsAsync(StringBuilder result, int deviceCount)
{
    for (int index = 0; index < deviceCount; index++)
    {
        result.AppendLine($"  测试设备 {index}:");

        // 测试打开操作
        uint hDongle = 0;
        uint openResult = Dongle_Open(ref hDongle, index);

        if (openResult != 0)
        {
            result.AppendLine($"    ❌ Open Dongle Failed! Return value: 0x{openResult:X8}");
            result.AppendLine($"       错误描述: {GetErrorDescription(openResult)}");
            continue;
        }

        result.AppendLine($"    ✅ Open Dongle Success! Handle: 0x{hDongle:X8}");
        openHandles.Add(hDongle);

        // 添加短暂延迟，模拟实际使用场景
        await Task.Delay(100);

        // 测试关闭操作
        uint closeResult = Dongle_Close(hDongle);

        if (closeResult != 0)
        {
            result.AppendLine($"    ❌ Close Dongle Failed! Return value: 0x{closeResult:X8}");
            result.AppendLine($"       错误描述: {GetErrorDescription(closeResult)}");
            result.AppendLine($"       句柄 0x{hDongle:X8} 可能未正确关闭");
        }
        else
        {
            result.AppendLine($"    ✅ Close Dongle Success! Handle: 0x{hDongle:X8}");
            openHandles.Remove(hDongle);
        }

        result.AppendLine();
    }
}

static async Task CleanupHandlesAsync(StringBuilder result)
{
    if (openHandles.Count > 0)
    {
        result.AppendLine("步骤4: 清理未关闭的句柄...");

        var handlesCopy = new List<uint>(openHandles);
        foreach (var handle in handlesCopy)
        {
            try
            {
                result.AppendLine($"  尝试关闭句柄: 0x{handle:X8}");
                uint closeResult = Dongle_Close(handle);

                if (closeResult == 0)
                {
                    result.AppendLine($"    ✅ 句柄 0x{handle:X8} 关闭成功");
                    openHandles.Remove(handle);
                }
                else
                {
                    result.AppendLine($"    ❌ 句柄 0x{handle:X8} 关闭失败: 0x{closeResult:X8}");
                    result.AppendLine($"       错误描述: {GetErrorDescription(closeResult)}");
                }
            }
            catch (Exception ex)
            {
                result.AppendLine($"    ❌ 关闭句柄 0x{handle:X8} 时发生异常: {ex.Message}");
            }

            // 添加延迟，避免操作过快
            await Task.Delay(50);
        }

        if (openHandles.Count > 0)
        {
            result.AppendLine($"⚠️  仍有 {openHandles.Count} 个句柄未能正确关闭");
        }
        else
        {
            result.AppendLine("✅ 所有句柄已正确关闭");
        }
    }
}

static async Task<string> RunStressTestAsync(int iterations = 10)
{
    var result = new StringBuilder();
    var stopwatch = Stopwatch.StartNew();

    result.AppendLine("=== 加密锁压力测试 ===");
    result.AppendLine($"测试轮数: {iterations}");
    result.AppendLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
    result.AppendLine();

    int successCount = 0;
    int failCount = 0;

    for (int i = 1; i <= iterations; i++)
    {
        result.AppendLine($"第 {i} 轮测试:");

        try
        {
            var testResult = await RunSingleOpenCloseTestAsync();
            if (testResult.Success)
            {
                successCount++;
                result.AppendLine($"  ✅ 成功 (耗时: {testResult.ElapsedMs} ms)");
            }
            else
            {
                failCount++;
                result.AppendLine($"  ❌ 失败: {testResult.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            failCount++;
            result.AppendLine($"  ❌ 异常: {ex.Message}");
        }

        // 轮次间延迟
        await Task.Delay(200);
    }

    stopwatch.Stop();

    result.AppendLine();
    result.AppendLine("=== 压力测试结果 ===");
    result.AppendLine($"总轮数: {iterations}");
    result.AppendLine($"成功: {successCount}");
    result.AppendLine($"失败: {failCount}");
    result.AppendLine($"成功率: {(double)successCount / iterations * 100:F2}%");
    result.AppendLine($"总耗时: {stopwatch.ElapsedMilliseconds} ms");
    result.AppendLine($"平均耗时: {stopwatch.ElapsedMilliseconds / iterations} ms/轮");

    return result.ToString();
}

static async Task<(bool Success, string ErrorMessage, long ElapsedMs)> RunSingleOpenCloseTestAsync()
{
    var stopwatch = Stopwatch.StartNew();

    try
    {
        // 枚举设备
        var pDongleInfo = new DONGLE_INFO();
        ushort pCount = 0;
        uint enumResult = Dongle_Enum(ref pDongleInfo, out pCount);

        if (enumResult != 0)
        {
            return (false, $"枚举失败: 0x{enumResult:X8}", stopwatch.ElapsedMilliseconds);
        }

        if (pCount == 0)
        {
            return (false, "未检测到设备", stopwatch.ElapsedMilliseconds);
        }

        // 打开设备
        uint hDongle = 0;
        uint openResult = Dongle_Open(ref hDongle, 0);

        if (openResult != 0)
        {
            return (false, $"打开失败: 0x{openResult:X8}", stopwatch.ElapsedMilliseconds);
        }

        // 短暂延迟
        await Task.Delay(10);

        // 关闭设备
        uint closeResult = Dongle_Close(hDongle);

        if (closeResult != 0)
        {
            return (false, $"关闭失败: 0x{closeResult:X8}", stopwatch.ElapsedMilliseconds);
        }

        return (true, string.Empty, stopwatch.ElapsedMilliseconds);
    }
    catch (Exception ex)
    {
        return (false, $"异常: {ex.Message}", stopwatch.ElapsedMilliseconds);
    }
}

// ===== 主程序入口 =====

    public static void Main(string[] args)
    {
        Console.OutputEncoding = Encoding.UTF8;
        Console.WriteLine("=== 加密锁测试控制台程序 ===");
        Console.WriteLine($"程序启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        Console.WriteLine();

        Console.WriteLine("开始执行测试（严格按照WinForm代码顺序）...");
        Console.WriteLine();

        // 严格按照您的WinForm Test_Click方法的顺序执行
        TestClick();

        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }

    // 严格按照您的WinForm Test_Click方法实现
    static void TestClick()
    {
        uint ret = 0;
        ushort pCount = 0;
        DONGLE_INFO pDongleInfo = new DONGLE_INFO();
        uint hDongle = 0;

        // 第一次枚举
        ret = Dongle_Enum(ref pDongleInfo, out pCount);
        if (ret != 0)
        {
            Console.WriteLine("Enum Dongle Failed! Return value:" + ret.ToString("X"));
            return;
        }
        else
        {
            Console.WriteLine("Enum Dongle Success!Count: " + pCount);
        }

        // 第二次枚举
        ret = Dongle_Enum(ref pDongleInfo, out pCount);
        if (ret != 0)
        {
            Console.WriteLine("GetInfo Dongle Failed! Return value:" + ret.ToString("X"));
            return;
        }
        else
        {
            Console.WriteLine("GetInfo Dongle Success!");
        }

        // 显示设备信息
        for (int k = 0; k < pCount; k++)
        {
            Console.WriteLine("\n*********Dongle ARM INFO*******\n");
            Console.WriteLine("The index: " + k);
            Console.WriteLine("Agent ID: " + pDongleInfo.m_Agent.ToString("X"));
            Console.WriteLine("Dev Type: " + pDongleInfo.m_DevType);
            Console.Write("HID: ");

            for (int i = 0; i < 8; i++)
            {
                Console.Write(pDongleInfo.m_HID[i].ToString("X") + "  ");
            }
            Console.WriteLine();

            Console.WriteLine("Brith day: 20" + pDongleInfo.m_BirthDay[0].ToString("X") + "-" + pDongleInfo.m_BirthDay[1].ToString("X") + "-" + pDongleInfo.m_BirthDay[2].ToString("X") + "  " + pDongleInfo.m_BirthDay[3].ToString("X") + ":" + pDongleInfo.m_BirthDay[4].ToString("X") + ":" + pDongleInfo.m_BirthDay[5].ToString("X"));
            Console.WriteLine("Is Mother Dongle: " + pDongleInfo.m_IsMother);
            Console.WriteLine("PID: " + pDongleInfo.m_PID.ToString("X"));
            Console.WriteLine("Product Type: " + pDongleInfo.m_Type.ToString("X"));
            Console.WriteLine("UID: " + pDongleInfo.m_UserID.ToString("X"));
        }

        // 打开设备
        ret = Dongle_Open(ref hDongle, 0);
        if (ret != 0)
        {
            Console.WriteLine("Open Dongle Failed! Return value:" + ret.ToString("X"));
            return;
        }
        else
        {
            Console.WriteLine("Open Dongle Success!");
        }

        // 关闭设备
        ret = Dongle_Close(hDongle);
        if (ret != 0)
        {
            Console.WriteLine("Close Dongle Failed! Return value:" + ret.ToString("X"));
            return;
        }
        else
        {
            Console.WriteLine("Close Dongle Success!");
        }
    }
}
{
    Console.WriteLine("请选择测试选项:");
    Console.WriteLine("1. 基本功能测试");
    Console.WriteLine("2. 压力测试 (10轮)");
    Console.WriteLine("3. 自定义压力测试");
    Console.WriteLine("4. 详细诊断测试");
    Console.WriteLine("5. WinForm兼容测试");
    Console.WriteLine("0. 退出程序");
    Console.WriteLine();
    Console.Write("请输入选项 (0-5): ");

    var input = Console.ReadLine();
    Console.WriteLine();

    try
    {
        switch (input)
        {
            case "1":
                Console.WriteLine("执行基本功能测试...");
                var result1 = await RunBasicTestAsync();
                Console.WriteLine(result1);
                break;

            case "2":
                Console.WriteLine("执行压力测试 (10轮)...");
                var result2 = await RunStressTestAsync(10);
                Console.WriteLine(result2);
                break;

            case "3":
                Console.Write("请输入测试轮数 (1-1000): ");
                var iterationsInput = Console.ReadLine();
                if (int.TryParse(iterationsInput, out int iterations) && iterations >= 1 && iterations <= 1000)
                {
                    Console.WriteLine($"执行自定义压力测试 ({iterations}轮)...");
                    var result3 = await RunStressTestAsync(iterations);
                    Console.WriteLine(result3);
                }
                else
                {
                    Console.WriteLine("❌ 无效的轮数，请输入1-1000之间的数字");
                }
                break;

            case "4":
                Console.WriteLine("执行详细诊断测试...");
                var result4 = await RunDiagnosticTestAsync();
                Console.WriteLine(result4);
                break;

            case "5":
                Console.WriteLine("执行WinForm兼容测试...");
                var result5 = await RunWinFormCompatibleTestAsync();
                Console.WriteLine(result5);
                break;

            case "0":
                Console.WriteLine("程序退出中...");

                // 最终清理
                if (openHandles.Count > 0)
                {
                    Console.WriteLine("正在清理未关闭的句柄...");
                    var cleanupResult = new StringBuilder();
                    await CleanupHandlesAsync(cleanupResult);
                    Console.WriteLine(cleanupResult.ToString());
                }

                Console.WriteLine("再见！");
                return;

            default:
                Console.WriteLine("❌ 无效选项，请重新选择");
                break;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ 执行测试时发生异常: {ex.Message}");
        Console.WriteLine($"   异常详情: {ex.StackTrace}");
    }

    Console.WriteLine();
    Console.WriteLine("按任意键继续...");
    Console.ReadKey();
    Console.Clear();

        Console.WriteLine("=== 加密锁测试控制台程序 ===");
        Console.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        Console.WriteLine();
        }
    }

// ===== 额外测试函数 =====

static async Task<string> RunDiagnosticTestAsync()
{
    var result = new StringBuilder();
    var stopwatch = Stopwatch.StartNew();

    result.AppendLine("=== 加密锁详细诊断测试 ===");
    result.AppendLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
    result.AppendLine();

    try
    {
        // 1. 环境检查
        result.AppendLine("步骤1: 环境检查");
        result.AppendLine($"  操作系统: {Environment.OSVersion}");
        result.AppendLine($"  .NET版本: {Environment.Version}");
        result.AppendLine($"  进程架构: {(Environment.Is64BitProcess ? "64" : "32")}位");
        result.AppendLine($"  工作目录: {Environment.CurrentDirectory}");

        // 检查DLL文件
        var dllPath = Path.Combine(Environment.CurrentDirectory, "Dongle_d.dll");
        if (File.Exists(dllPath))
        {
            var fileInfo = new FileInfo(dllPath);
            result.AppendLine($"  ✅ DLL文件存在: {dllPath}");
            result.AppendLine($"     文件大小: {fileInfo.Length} 字节");
            result.AppendLine($"     修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
        }
        else
        {
            result.AppendLine($"  ❌ DLL文件不存在: {dllPath}");
            return result.ToString();
        }
        result.AppendLine();

        // 2. 多次枚举测试
        result.AppendLine("步骤2: 多次枚举测试");
        for (int i = 1; i <= 5; i++)
        {
            var enumStopwatch = Stopwatch.StartNew();
            var pDongleInfo = new DONGLE_INFO();
            ushort pCount = 0;
            uint enumResult = Dongle_Enum(ref pDongleInfo, out pCount);
            enumStopwatch.Stop();

            result.AppendLine($"  第{i}次枚举: ");
            if (enumResult == 0)
            {
                result.AppendLine($"    ✅ 成功，设备数量: {pCount}，耗时: {enumStopwatch.ElapsedMilliseconds}ms");
            }
            else
            {
                result.AppendLine($"    ❌ 失败，错误代码: 0x{enumResult:X8}，耗时: {enumStopwatch.ElapsedMilliseconds}ms");
                result.AppendLine($"       错误描述: {GetErrorDescription(enumResult)}");
            }

            await Task.Delay(100);
        }
        result.AppendLine();

        // 3. 详细的打开关闭测试
        result.AppendLine("步骤3: 详细的打开关闭测试");
        await RunDetailedOpenCloseTestAsync(result);

    }
    catch (Exception ex)
    {
        result.AppendLine($"❌ 诊断测试过程中发生异常: {ex.Message}");
        result.AppendLine($"   异常堆栈: {ex.StackTrace}");
    }
    finally
    {
        stopwatch.Stop();
        result.AppendLine();
        result.AppendLine($"诊断测试结束时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        result.AppendLine($"总耗时: {stopwatch.ElapsedMilliseconds} ms");
    }

    return result.ToString();
}

static async Task RunDetailedOpenCloseTestAsync(StringBuilder result)
{
    // 先枚举获取设备信息
    var pDongleInfo = new DONGLE_INFO();
    ushort pCount = 0;
    uint enumResult = Dongle_Enum(ref pDongleInfo, out pCount);

    if (enumResult != 0 || pCount == 0)
    {
        result.AppendLine("  ❌ 无法获取设备信息，跳过打开关闭测试");
        return;
    }

    for (int deviceIndex = 0; deviceIndex < pCount; deviceIndex++)
    {
        result.AppendLine($"  测试设备 {deviceIndex}:");

        // 多次打开关闭测试
        for (int attempt = 1; attempt <= 3; attempt++)
        {
            result.AppendLine($"    第{attempt}次尝试:");

            var openStopwatch = Stopwatch.StartNew();
            uint hDongle = 0;
            uint openResult = Dongle_Open(ref hDongle, deviceIndex);
            openStopwatch.Stop();

            if (openResult != 0)
            {
                result.AppendLine($"      ❌ 打开失败: 0x{openResult:X8} ({GetErrorDescription(openResult)})");
                result.AppendLine($"         打开耗时: {openStopwatch.ElapsedMilliseconds}ms");
                continue;
            }

            result.AppendLine($"      ✅ 打开成功: 句柄=0x{hDongle:X8}，耗时: {openStopwatch.ElapsedMilliseconds}ms");

            // 等待一段时间
            await Task.Delay(50);

            // 尝试关闭
            var closeStopwatch = Stopwatch.StartNew();
            uint closeResult = Dongle_Close(hDongle);
            closeStopwatch.Stop();

            if (closeResult != 0)
            {
                result.AppendLine($"      ❌ 关闭失败: 0x{closeResult:X8} ({GetErrorDescription(closeResult)})");
                result.AppendLine($"         关闭耗时: {closeStopwatch.ElapsedMilliseconds}ms");

                // 尝试强制关闭
                result.AppendLine($"      🔄 尝试再次关闭...");
                await Task.Delay(100);
                uint retryCloseResult = Dongle_Close(hDongle);
                if (retryCloseResult == 0)
                {
                    result.AppendLine($"      ✅ 重试关闭成功");
                }
                else
                {
                    result.AppendLine($"      ❌ 重试关闭仍失败: 0x{retryCloseResult:X8}");
                }
            }
            else
            {
                result.AppendLine($"      ✅ 关闭成功，耗时: {closeStopwatch.ElapsedMilliseconds}ms");
            }

            // 尝试间隔
            await Task.Delay(200);
        }
        result.AppendLine();
    }
}

static async Task<string> RunWinFormCompatibleTestAsync()
{
    var result = new StringBuilder();
    var stopwatch = Stopwatch.StartNew();

    result.AppendLine("=== WinForm兼容测试 ===");
    result.AppendLine("完全模拟您的WinForm代码执行流程");
    result.AppendLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
    result.AppendLine();

    try
    {
        // 完全按照WinForm代码的变量声明和调用顺序
        uint ret = 0;
        ushort pCount = 0;
        DONGLE_INFO pDongleInfo = new DONGLE_INFO();
        uint hDongle = 0;

        // 第一次枚举 - 完全按照WinForm代码
        result.AppendLine("步骤1: 第一次枚举（获取数量）");
        ret = Dongle_Enum(ref pDongleInfo, out pCount);
        if (ret != 0)
        {
            result.AppendLine($"❌ Enum Dongle Failed! Return value: 0x{ret:X8}");
            result.AppendLine($"   错误描述: {GetErrorDescription(ret)}");
            return result.ToString();
        }
        result.AppendLine($"✅ Enum Dongle Success! Count: {pCount}");

        // 第二次枚举 - 完全按照WinForm代码
        result.AppendLine();
        result.AppendLine("步骤2: 第二次枚举（获取信息）");
        ret = Dongle_Enum(ref pDongleInfo, out pCount);
        if (ret != 0)
        {
            result.AppendLine($"❌ GetInfo Dongle Failed! Return value: 0x{ret:X8}");
            result.AppendLine($"   错误描述: {GetErrorDescription(ret)}");
            return result.ToString();
        }
        result.AppendLine("✅ GetInfo Dongle Success!");

        // 显示设备信息 - 完全按照WinForm格式
        result.AppendLine();
        for (int k = 0; k < pCount; k++)
        {
            result.AppendLine($"*********Dongle ARM INFO*******");
            result.AppendLine($"The index: {k}");
            result.AppendLine($"Agent ID: {pDongleInfo.m_Agent:X}");
            result.AppendLine($"Dev Type: {pDongleInfo.m_DevType}");
            result.Append("HID: ");
            for (int i = 0; i < 8; i++)
            {
                result.Append($"{pDongleInfo.m_HID[i]:X}  ");
            }
            result.AppendLine();
            result.AppendLine($"Brith day: 20{pDongleInfo.m_BirthDay[0]:X}-{pDongleInfo.m_BirthDay[1]:X}-{pDongleInfo.m_BirthDay[2]:X}  {pDongleInfo.m_BirthDay[3]:X}:{pDongleInfo.m_BirthDay[4]:X}:{pDongleInfo.m_BirthDay[5]:X}");
            result.AppendLine($"Is Mother Dongle: {pDongleInfo.m_IsMother}");
            result.AppendLine($"PID: {pDongleInfo.m_PID:X}");
            result.AppendLine($"Product Type: {pDongleInfo.m_Type:X}");
            result.AppendLine($"UID: {pDongleInfo.m_UserID:X}");
        }

        // 打开设备 - 完全按照WinForm代码
        result.AppendLine();
        result.AppendLine("步骤3: 打开设备（WinForm方式）");
        ret = Dongle_Open(ref hDongle, 0);
        if (ret != 0)
        {
            result.AppendLine($"❌ Open Dongle Failed! Return value: 0x{ret:X8}");
            result.AppendLine($"   错误描述: {GetErrorDescription(ret)}");
            return result.ToString();
        }
        result.AppendLine($"✅ Open Dongle Success! Handle: 0x{hDongle:X8}");

        // 关闭设备 - 完全按照WinForm代码
        result.AppendLine();
        result.AppendLine("步骤4: 关闭设备（WinForm方式）");
        ret = Dongle_Close(hDongle);
        if (ret != 0)
        {
            result.AppendLine($"❌ Close Dongle Failed! Return value: 0x{ret:X8}");
            result.AppendLine($"   错误描述: {GetErrorDescription(ret)}");
            result.AppendLine($"   这与您报告的问题一致！");

            // 尝试不同的关闭策略
            result.AppendLine();
            result.AppendLine("尝试替代关闭策略:");

            // 策略1: 延迟后重试
            result.AppendLine("  策略1: 延迟100ms后重试关闭");
            await Task.Delay(100);
            uint retryResult1 = Dongle_Close(hDongle);
            result.AppendLine($"    结果: 0x{retryResult1:X8} - {GetErrorDescription(retryResult1)}");

            // 策略2: 更长延迟后重试
            result.AppendLine("  策略2: 延迟500ms后重试关闭");
            await Task.Delay(500);
            uint retryResult2 = Dongle_Close(hDongle);
            result.AppendLine($"    结果: 0x{retryResult2:X8} - {GetErrorDescription(retryResult2)}");

            // 策略3: 重新枚举后关闭
            result.AppendLine("  策略3: 重新枚举设备后关闭");
            var tempInfo = new DONGLE_INFO();
            ushort tempCount = 0;
            Dongle_Enum(ref tempInfo, out tempCount);
            uint retryResult3 = Dongle_Close(hDongle);
            result.AppendLine($"    结果: 0x{retryResult3:X8} - {GetErrorDescription(retryResult3)}");

            return result.ToString();
        }
        result.AppendLine($"✅ Close Dongle Success!");

    }
    catch (Exception ex)
    {
        result.AppendLine($"❌ 测试过程中发生异常: {ex.Message}");
        result.AppendLine($"   异常堆栈: {ex.StackTrace}");
    }
    finally
    {
        stopwatch.Stop();
        result.AppendLine();
        result.AppendLine($"测试结束时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
        result.AppendLine($"总耗时: {stopwatch.ElapsedMilliseconds} ms");
    }

    return result.ToString();
    }
}

