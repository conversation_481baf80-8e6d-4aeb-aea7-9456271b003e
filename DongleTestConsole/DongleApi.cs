using System.Runtime.InteropServices;
using static DongleTestConsole.DongleStructures;

namespace DongleTestConsole;

/// <summary>
/// 加密锁API接口定义
/// </summary>
public static class DongleApi
{
    // 常量定义
    public const string DLL_NAME = "Dongle_d.dll";

    #region DLL导入声明

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_Enum(ref DONGLE_INFO pDongleInfo, out ushort pCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_Open(ref uint phDongle, int nIndex);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_Close(uint hDongle);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_VerifyPIN(uint hDongle, uint nFlags, byte[] pPIN, out int pRemainCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_CreateFile(uint hDongle, uint nFileType, ushort wFileID, uint pFileAttr);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_WriteFile(uint hDongle, uint nFileType, ushort wFileID, short wOffset, byte[] buffer, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ReadFile(uint hDongle, short wFileID, short wOffset, byte[] buffer, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ListFile(uint hDongle, uint nFileType, DATA_FILE_LIST[] pFileList, ref int pDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_DeleteFile(uint hDongle, uint nFileType, short wFileID);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_DownloadExeFile(uint hDongle, EXE_FILE_INFO[] pExeFileInfo, int nCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RunExeFile(uint hDongle, short wFileID, byte[] pInOutData, short wInOutDataLen, ref int nMainRet);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_WriteShareMemory(uint hDongle, byte[] pData, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ReadShareMemory(uint hDongle, byte[] pData);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_WriteData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ReadData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_LEDControl(uint hDongle, uint nFlag);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SwitchProtocol(uint hDongle, uint nFlag);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_GetUTCTime(uint hDongle, ref uint pdwUTCTime);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SetDeadline(uint hDongle, uint dwTime);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_GenUniqueKey(uint hDongle, int nSeedLen, byte[] pSeed, byte[] pPIDstr, byte[] pAdminPINstr);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ResetState(uint hDongle);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ChangePIN(uint hDongle, uint nFlags, byte[] pOldPIN, byte[] pNewPIN, int nTryCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RFS(uint hDongle);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SetUserID(uint hDongle, uint dwUserID);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_ResetUserPIN(uint hDongle, byte[] pAdminPIN);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RsaGenPubPriKey(uint hDongle, ushort wPriFileID, ref RSA_PUBLIC_KEY pPubBakup, ref RSA_PRIVATE_KEY pPriBakup);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RsaPri(uint hDongle, ushort wPriFileID, uint nFlag, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_RsaPub(uint hDongle, uint nFlag, ref RSA_PUBLIC_KEY pPubKey, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_TDES(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SM4(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_HASH(uint hDongle, uint nFlag, byte[] pInData, uint nDataLen, byte[] pHash);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_LimitSeedCount(uint hDongle, int nCount);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_Seed(uint hDongle, byte[] pSeed, uint nSeedLen, byte[] pOutData);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_EccGenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY vPubBakup, ref ECCSM2_PRIVATE_KEY vPriBakup);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_EccSign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_EccVerify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SM2GenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY pPubBakup, ref ECCSM2_PRIVATE_KEY pPriBakup);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SM2Sign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

    [DllImport(DLL_NAME)]
    public static extern uint Dongle_SM2Verify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

    #endregion

    #region 错误代码定义

    /// <summary>
    /// 获取错误代码的描述
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <returns>错误描述</returns>
    public static string GetErrorDescription(uint errorCode)
    {
        return errorCode switch
        {
            0x00000000 => "成功",
            0x00000001 => "无效的参数",
            0x00000002 => "内存不足",
            0x00000003 => "设备未找到",
            0x00000004 => "设备已打开",
            0x00000005 => "设备未打开",
            0x00000006 => "通信错误",
            0x00000007 => "操作超时",
            0x00000008 => "权限不足",
            0x00000009 => "文件不存在",
            0x0000000A => "文件已存在",
            0x0000000B => "空间不足",
            0x0000000C => "PIN码错误",
            0x0000000D => "PIN码被锁定",
            0x0000000E => "操作被拒绝",
            0x0000000F => "数据校验错误",

            // 厂商特定错误代码
            0xF0000001 => "设备通信失败",
            0xF0000002 => "设备句柄无效或设备已断开连接",
            0xF0000003 => "设备忙碌或被其他程序占用",
            0xF0000004 => "设备驱动程序错误",
            0xF0000005 => "设备固件错误",

            _ => $"未知错误代码: 0x{errorCode:X8}"
        };
    }

    #endregion
}
