<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Platform>x64</Platform>
  </PropertyGroup>

  <ItemGroup>
    <None Update="Dongle_d.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="DongleApi.cs" />
    <Compile Remove="DongleTester.cs" />
  </ItemGroup>

</Project>
